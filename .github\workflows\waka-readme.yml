name: Advanced WakaTime Stats

on:
  schedule:
    - cron: '0 */8 * * *'  # Runs every 8 hours for better performance
  workflow_dispatch:

jobs:
  update-readme:
    name: Update Advanced WakaTime Metrics
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - name: Update WakaTime Stats
        # Note: WAKATIME_API_KEY secret needs to be configured in repository settings
        uses: anmol098/waka-readme-stats@master
        with:
          WAKATIME_API_KEY: ${{ secrets.WAKATIME_API_KEY }}
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

          # Enhanced Display Options
          SHOW_OS: "True"
          SHOW_PROJECTS: "True"
          SHOW_PROFILE_VIEWS: "False"
          SHOW_EDITORS: "True"
          SHOW_LANGUAGE_PER_REPO: "True"
          SHOW_LOC_CHART: "True"
          SHOW_LINES_OF_CODE: "True"
          SHOW_SHORT_INFO: "True"
          SHOW_TIMEZONE: "True"
          SHOW_LANGUAGE: "True"
          SHOW_TOTAL_CODE_TIME: "True"
          SHOW_COMMIT: "True"
          SHOW_DAYS_OF_WEEK: "True"
          SHOW_UPDATED_DATE: "True"

          # Additional Features
          LOCALE: "en"
